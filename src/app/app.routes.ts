import { Routes } from "@angular/router";
import { PageNotFoundComponent } from "./pages/page-not-found/page-not-found.component";

export const routes: Routes = [
  {
    path: "not-found",
    component: PageNotFoundComponent,
  },
  {
    path: "game1",
    loadChildren: () =>
      import("./pages/game-normal/game-normal.routes").then(
        (m) => m.GameNormalRoutes
      ),
  },
  {
    path: "game2",
    loadChildren: () =>
      import("./pages/game-oa/game-oa.routes").then((m) => m.GameOARoutes),
  },
  {
    path: "**",
    redirectTo: "not-found",
    pathMatch: "full",
  },
];
