import { Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";

export interface PopupState {
  type: "notification" | "code" | null;
  message?: string;
}

@Injectable({
  providedIn: "root",
})
export class PopupService {
  private popupState = new BehaviorSubject<PopupState>({ type: null });

  popupState$ = this.popupState.asObservable();

  showNotification(message: string) {
    this.popupState.next({ type: "notification", message });
  }

  showCodeInput() {
    this.popupState.next({ type: "code" });
  }

  closePopup() {
    this.popupState.next({ type: null });
  }
}
