import { Component, EventEmitter, Output } from "@angular/core";
import { FormsModule } from "@angular/forms";

@Component({
  selector: "app-code-input-popup",
  standalone: true,
  imports: [FormsModule],
  templateUrl: "./code-input-popup.component.html",
  styleUrl: "./code-input-popup.component.scss",
})
export class CodeInputPopupComponent {
  code: string = "";

  @Output() codeSubmit = new EventEmitter<string>();

  submitCode() {
    this.codeSubmit.emit(this.code);
  }
}
