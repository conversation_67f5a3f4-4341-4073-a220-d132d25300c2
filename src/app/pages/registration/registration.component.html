<!-- Popup overlay đặt cuối file -->
<div *ngIf="(popupService.popupState$ | async) as popup">
  <div class="popup-overlay" *ngIf="popup.type">
    <app-notification-popup
      *ngIf="popup.type === 'notification'"
      [message]="popup.message ?? ''"
    ></app-notification-popup>
    <app-code-input-popup
      *ngIf="popup.type === 'code'"
      (codeSubmit)="handleCode($event)"
    ></app-code-input-popup>
  </div>
</div>
<button (click)="openNotification('Đ<PERSON>y là thông báo!')">
  Bật popup thông báo
</button>
<button (click)="openCodeInput()">Bật popup nhập mã code</button>
