import { Component } from "@angular/core";
import { PopupService } from "../../helpers/popup.service";
import { CodeInputPopupComponent } from "../../shared/code-input-popup/code-input-popup.component";
import { NotificationPopupComponent } from "../../shared/notification-popup/notification-popup.component";
import { FormsModule } from "@angular/forms";
import { CommonModule } from "@angular/common";
import { UtilsService } from "../../helpers/utils.service";

@Component({
  selector: "app-registration",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CodeInputPopupComponent,
    NotificationPopupComponent,
  ],
  templateUrl: "./registration.component.html",
  styleUrl: "./registration.component.scss",
})
export class RegistrationComponent {
  name: string = "";
  phone: string = "";
  code: string = "";
  constructor(public popupService: PopupService, private utils: UtilsService) {}

  openNotification(message: string) {
    this.popupService.showNotification(message);
  }

  openCodeInput() {
    this.popupService.showCodeInput();
  }

  closePopup() {
    this.popupService.closePopup();
    this.popupService.closePopup();
  }

  handleCode(code: string) {
    // Xử lý mã code nhận được ở đây
    console.log("Mã code nhận được:", code);
    this.popupService.closePopup();
  }

  playGame() {
    if (!this.utils.validationForm(this.name, this.phone)) {
      this.openNotification("Vui lòng nhập đầy đủ thông tin!");
      return;
    }

    const phoneCheck = this.utils.validatePhone(this.phone);
    if (phoneCheck.status !== 0) {
      this.openNotification("Số điện thoại không hợp lệ!");
      return;
    }

    this.onCheckPhoneJoined(phoneCheck.phone);
  }

  onCheckPhoneJoined(phone: string) {
    // Xử lý khi số điện thoại đã tham gia
    console.log("Số điện thoại đã tham gia:", phone);
    this.openNotification("Số điện thoại đã tham gia!");
    this.openCodeInput();
    this.popupService.closePopup();
  }
}
