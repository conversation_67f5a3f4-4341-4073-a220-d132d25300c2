import { Component } from "@angular/core";
import { gsap } from "gsap";
import { ApiService } from "../../helpers/api.service";
import { UtilsService } from "../../helpers/utils.service";

@Component({
  selector: "app-game-play",
  standalone: true,
  imports: [],
  templateUrl: "./game-play.component.html",
  styleUrl: "./game-play.component.scss",
})
export class GamePlayComponent {
  constructor(private apiService: ApiService, private utils: UtilsService) {}
}
