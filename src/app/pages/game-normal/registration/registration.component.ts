import { Component } from "@angular/core";
import { BRANCH_OPTION, BranchOption } from "../../../constants/branch-option";
import { CommonModule } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";

@Component({
  selector: "app-registration",
  standalone: true,
  imports: [CommonModule],
  templateUrl: "./registration.component.html",
  styleUrl: "./registration.component.scss",
})
export class RegistrationComponent {
  branchOption: BranchOption[] = BRANCH_OPTION;

  constructor(private router: Router, private route: ActivatedRoute) {}

  onSubmit() {
    console.log("onSubmit");

    // <PERSON><PERSON> lý validate nếu cần
    this.router.navigate(["game-play"], { relativeTo: this.route });
  }
}
