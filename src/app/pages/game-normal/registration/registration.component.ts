import { Component } from "@angular/core";
import { BRANCH_OPTION, BranchOption } from "../../../constants/branch-option";
import { CommonModule } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";

@Component({
  selector: "app-registration",
  standalone: true,
  imports: [CommonModule],
  templateUrl: "./registration.component.html",
  styleUrl: "./registration.component.scss",
})
export class RegistrationComponent {
  branchOption: BranchOption[] = BRANCH_OPTION;

  constructor(private router: Router, private route: ActivatedRoute) {}

  onSubmit(event: Event, nameInput: HTMLInputElement, phoneInput: HTMLInputElement, branchSelect: HTMLSelectElement) {
    event.preventDefault(); // Ngăn chặn form reload trang

    // Lấy dữ liệu từ form
    const formData = {
      name: nameInput.value,
      phone: phoneInput.value,
      branch: branchSelect.value
    };

    console.log("Form data:", formData);

    // <PERSON><PERSON> lý validate nếu cần
    if (!formData.name || !formData.phone || !formData.branch) {
      alert("<PERSON>ui lòng điền đầy đủ thông tin!");
      return;
    }

    this.router.navigate(["game-play"], { relativeTo: this.route });
  }
}
