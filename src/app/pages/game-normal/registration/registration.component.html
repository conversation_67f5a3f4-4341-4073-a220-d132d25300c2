<div class="main-layout">
  <div class="form-container">
    <div class="registration-form">
      <div class="form-row">
        <label for="name"><PERSON><PERSON> tên</label>
        <input id="name" type="text" #nameInput />
      </div>
      <div class="form-row">
        <label for="phone"><PERSON><PERSON> điện thoại</label>
        <input id="phone" type="text" #phoneInput />
      </div>
      <div class="form-row">
        <label for="branch">Chi nhánh</label>
        <select id="branch" #branchSelect>
          <option value="">Chọn chi nhánh</option>
          <option *ngFor="let opt of branchOption" [value]="opt.value"
            >{{ opt.label }}</option
          >
        </select>
      </div>
      <div class="form-actions">
        <button (click)="onSubmit(nameInput, phoneInput, branchSelect)"><PERSON><PERSON><PERSON> k<PERSON></button>
      </div>
    </div>
  </div>
</div>
