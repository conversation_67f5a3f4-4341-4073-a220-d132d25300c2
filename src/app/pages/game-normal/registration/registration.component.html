<div class="main-layout">
  <div class="form-container">
    <div class="form-header"></div>
    <div class="form-body">
      <form (ngSubmit)="onSubmit()">
        <div class="form-row">
          <label for="name"><PERSON><PERSON> tên</label>
          <input id="name" type="text" />
        </div>
        <div class="form-row">
          <label for="phone">Số điện thoại</label>
          <input id="phone" type="text" />
        </div>
        <div class="form-row">
          <label for="branch">Chi nhánh</label>
          <select id="branch">
            <option value="">Ch<PERSON>n chi nhánh</option>
            <option *ngFor="let opt of branchOption" [value]="opt.value"
              >{{ opt.label }}</option
            >
          </select>
        </div>
        <div class="form-actions">
          <button type="submit"><PERSON><PERSON><PERSON> k<PERSON></button>
        </div>
      </form>
    </div>
  </div>
</div>
