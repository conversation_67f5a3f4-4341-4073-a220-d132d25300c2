.main-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-color, #f5f5f5);
}

.form-container {
  width: 100vw;
  max-width: 400px;
  min-height: 100vh;
  margin: 0;
  padding: 2rem 1rem;
  background: #fff;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

@media (min-width: 600px) {
  .form-container {
    min-height: 700px;
    max-width: 400px;
    border-radius: 16px;
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
    margin: 40px auto;
  }
}
